#!/bin/bash
# Windows 10 Git Bash 环境设置脚本
# 作者: Augment Agent
# 版本: 1.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的文本
print_color() {
    local color=$1
    local text=$2
    echo -e "${color}${text}${NC}"
}

print_header() {
    echo ""
    print_color $PURPLE "==============================================="
    print_color $PURPLE "    $1"
    print_color $PURPLE "==============================================="
    echo ""
}

print_step() {
    print_color $CYAN "[$1] $2"
}

print_success() {
    print_color $GREEN "    ✓ $1"
}

print_warning() {
    print_color $YELLOW "    ⚠ $1"
}

print_error() {
    print_color $RED "    ✗ $1"
}

# 检查Git Bash环境
check_git_bash() {
    print_step "1/7" "检查Git Bash环境..."
    
    if [ -n "$BASH_VERSION" ]; then
        print_success "Git Bash 版本: $BASH_VERSION"
    else
        print_error "不在Git Bash环境中"
        return 1
    fi
    
    # 检查常用命令
    local commands=("curl" "grep" "sed" "awk" "tail" "head")
    for cmd in "${commands[@]}"; do
        if command -v "$cmd" > /dev/null 2>&1; then
            print_success "$cmd 命令可用"
        else
            print_warning "$cmd 命令不可用"
        fi
    done
    
    echo ""
    return 0
}

# 设置环境变量
setup_environment_variables() {
    print_step "2/7" "设置环境变量..."
    
    # 设置临时环境变量
    export ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
    export ANTHROPIC_BASE_URL="https://anyrouter.top"
    
    print_success "ANTHROPIC_AUTH_TOKEN: ${ANTHROPIC_AUTH_TOKEN:0:10}..."
    print_success "ANTHROPIC_BASE_URL: $ANTHROPIC_BASE_URL"
    
    # 检查是否需要永久设置
    if [ ! -f ~/.bashrc ] || ! grep -q "ANTHROPIC_AUTH_TOKEN" ~/.bashrc; then
        print_warning "环境变量未永久设置"
        read -p "是否要永久设置环境变量到 ~/.bashrc? (y/n): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            setup_permanent_env
        fi
    else
        print_success "环境变量已永久设置"
    fi
    
    echo ""
}

# 永久设置环境变量
setup_permanent_env() {
    print_step "2.1/7" "永久设置环境变量..."
    
    # 备份现有的bashrc
    if [ -f ~/.bashrc ]; then
        cp ~/.bashrc ~/.bashrc.backup.$(date +%Y%m%d-%H%M%S)
        print_success "已备份现有的 ~/.bashrc"
    fi
    
    # 添加环境变量到bashrc
    cat >> ~/.bashrc << 'EOF'

# Claude Code 环境变量 (由 setup-environment.sh 添加)
export ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
export ANTHROPIC_BASE_URL="https://anyrouter.top"

# Claude Code 别名
alias claude-start='claude --dangerously-skip-permissions'
alias claude-status='ps aux | grep claude'
alias claude-env='echo "Token: ${ANTHROPIC_AUTH_TOKEN:0:10}..." && echo "URL: $ANTHROPIC_BASE_URL"'
EOF
    
    print_success "环境变量已添加到 ~/.bashrc"
    print_success "已添加便捷别名: claude-start, claude-status, claude-env"
    
    # 重新加载bashrc
    source ~/.bashrc
    print_success "已重新加载 ~/.bashrc"
}

# 检查Node.js和npm
check_nodejs() {
    print_step "3/7" "检查Node.js环境..."
    
    if command -v node > /dev/null 2>&1; then
        local node_version=$(node --version)
        print_success "Node.js 版本: $node_version"
    else
        print_error "Node.js 未安装"
        print_warning "请从 https://nodejs.org 下载安装 Node.js"
        return 1
    fi
    
    if command -v npm > /dev/null 2>&1; then
        local npm_version=$(npm --version)
        print_success "npm 版本: $npm_version"
    else
        print_error "npm 未安装"
        return 1
    fi
    
    echo ""
    return 0
}

# 检查Claude Code安装
check_claude_code() {
    print_step "4/7" "检查Claude Code安装..."
    
    if command -v claude > /dev/null 2>&1; then
        local claude_version=$(claude --version 2>/dev/null || echo "版本检查失败")
        print_success "Claude Code 已安装: $claude_version"
    else
        print_error "Claude Code 未安装"
        read -p "是否要安装 Claude Code? (y/n): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_claude_code
        else
            print_warning "跳过 Claude Code 安装"
        fi
    fi
    
    echo ""
}

# 安装Claude Code
install_claude_code() {
    print_step "4.1/7" "安装Claude Code..."
    
    if command -v npm > /dev/null 2>&1; then
        print_color $CYAN "正在安装 Claude Code..."
        if npm install -g @anthropic-ai/claude-cli; then
            print_success "Claude Code 安装成功"
        else
            print_error "Claude Code 安装失败"
            print_warning "请手动运行: npm install -g @anthropic-ai/claude-cli"
        fi
    else
        print_error "npm 不可用，无法安装 Claude Code"
    fi
}

# 检查网络连接
check_network() {
    print_step "5/7" "检查网络连接..."
    
    # 检查基本网络连接
    if ping -c 1 google.com > /dev/null 2>&1; then
        print_success "基本网络连接正常"
    else
        print_warning "基本网络连接可能有问题"
    fi
    
    # 检查API端点
    if curl -s --connect-timeout 5 "$ANTHROPIC_BASE_URL" > /dev/null 2>&1; then
        print_success "API端点连接正常: $ANTHROPIC_BASE_URL"
    else
        print_warning "API端点连接可能有问题: $ANTHROPIC_BASE_URL"
        print_warning "请检查代理设置或网络防火墙"
    fi
    
    echo ""
}

# 创建项目结构
setup_project_structure() {
    print_step "6/7" "设置项目结构..."
    
    # 创建必要的目录
    local dirs=("logs" "backups" "scripts" "docs")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "创建目录: $dir"
        else
            print_success "目录已存在: $dir"
        fi
    done
    
    # 创建.gitignore文件
    if [ ! -f .gitignore ]; then
        cat > .gitignore << 'EOF'
# Claude Code 相关文件
.claude_pid
.current_task
.current_progress_file
claude-session*.log
project-progress-*.md

# 日志和备份
logs/
backups/

# 临时文件
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db
EOF
        print_success "创建 .gitignore 文件"
    else
        print_success ".gitignore 文件已存在"
    fi
    
    echo ""
}

# 创建快捷脚本
create_shortcuts() {
    print_step "7/7" "创建快捷脚本..."
    
    # 创建快速启动脚本
    cat > scripts/quick-start.sh << 'EOF'
#!/bin/bash
# 快速启动 Claude Code

echo "快速启动 Claude Code..."

# 检查环境变量
if [ -z "$ANTHROPIC_AUTH_TOKEN" ]; then
    echo "设置环境变量..."
    export ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
    export ANTHROPIC_BASE_URL="https://anyrouter.top"
fi

# 启动Claude Code
claude --dangerously-skip-permissions
EOF
    chmod +x scripts/quick-start.sh
    print_success "创建快速启动脚本: scripts/quick-start.sh"
    
    # 创建环境检查脚本
    cat > scripts/check-env.sh << 'EOF'
#!/bin/bash
# 环境检查脚本

echo "=== Claude Code 环境检查 ==="
echo "Git Bash: $BASH_VERSION"
echo "Node.js: $(node --version 2>/dev/null || echo '未安装')"
echo "npm: $(npm --version 2>/dev/null || echo '未安装')"
echo "Claude: $(claude --version 2>/dev/null || echo '未安装')"
echo "Token: ${ANTHROPIC_AUTH_TOKEN:0:10}..."
echo "URL: $ANTHROPIC_BASE_URL"
echo "工作目录: $(pwd)"
echo "=========================="
EOF
    chmod +x scripts/check-env.sh
    print_success "创建环境检查脚本: scripts/check-env.sh"
    
    echo ""
}

# 显示完成信息
show_completion() {
    print_header "设置完成"
    
    print_color $GREEN "环境设置已完成！"
    echo ""
    
    print_color $CYAN "可用的命令："
    echo "  claude-start          # 启动 Claude Code"
    echo "  claude-env            # 显示环境变量"
    echo "  claude-status         # 检查 Claude 进程"
    echo "  ./scripts/quick-start.sh    # 快速启动"
    echo "  ./scripts/check-env.sh      # 环境检查"
    echo ""
    
    print_color $CYAN "启动方式："
    echo "  1. 直接运行: claude --dangerously-skip-permissions"
    echo "  2. 使用别名: claude-start"
    echo "  3. 使用脚本: ./scripts/quick-start.sh"
    echo "  4. 使用批处理: start-claude.bat (Windows)"
    echo "  5. 使用PowerShell: start-claude.ps1"
    echo ""
    
    print_color $YELLOW "重要提醒："
    echo "  - 在 Claude Code 中输入命令后记得按回车键"
    echo "  - 等待 AI 完成回复后再继续下一个命令"
    echo "  - 使用 /help 查看 Claude Code 的内置命令"
    echo "  - 使用 /exit 退出 Claude Code"
    echo ""
}

# 主函数
main() {
    print_header "Windows 10 Claude Code 环境设置"
    
    # 执行所有检查和设置步骤
    check_git_bash || exit 1
    setup_environment_variables
    check_nodejs || print_warning "Node.js 环境有问题，可能影响 Claude Code 使用"
    check_claude_code
    check_network
    setup_project_structure
    create_shortcuts
    
    show_completion
}

# 运行主函数
main "$@"
