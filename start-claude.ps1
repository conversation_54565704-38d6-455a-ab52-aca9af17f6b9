# Windows 10 Claude Code PowerShell 启动脚本
# 作者: Augment Agent
# 版本: 1.0

# 设置控制台标题
$Host.UI.RawUI.WindowTitle = "Claude Code 启动器"

# 颜色定义
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Colors[$Color]
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

function Test-NetworkConnection {
    param([string]$Url)
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# 主函数
function Start-ClaudeCode {
    Clear-Host
    
    Write-ColorText "===============================================" "Header"
    Write-ColorText "    Windows 10 Claude Code 启动器" "Header"
    Write-ColorText "===============================================" "Header"
    Write-Host ""
    
    # 步骤1: 设置环境变量
    Write-ColorText "[1/6] 设置环境变量..." "Info"
    $env:ANTHROPIC_AUTH_TOKEN = "sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
    $env:ANTHROPIC_BASE_URL = "https://anyrouter.top"
    
    Write-ColorText "    ✓ ANTHROPIC_AUTH_TOKEN: $($env:ANTHROPIC_AUTH_TOKEN.Substring(0,10))..." "Success"
    Write-ColorText "    ✓ ANTHROPIC_BASE_URL: $env:ANTHROPIC_BASE_URL" "Success"
    Write-Host ""
    
    # 步骤2: 检查PowerShell版本
    Write-ColorText "[2/6] 检查PowerShell环境..." "Info"
    Write-ColorText "    ✓ PowerShell 版本: $($PSVersionTable.PSVersion)" "Success"
    Write-ColorText "    ✓ 操作系统: $($PSVersionTable.OS)" "Success"
    Write-Host ""
    
    # 步骤3: 检查Git Bash
    Write-ColorText "[3/6] 检查Git Bash环境..." "Info"
    $gitBashPath = "C:\Program Files\git\bin\bash.exe"
    if (Test-Path $gitBashPath) {
        Write-ColorText "    ✓ Git Bash 已安装: $gitBashPath" "Success"
    } else {
        Write-ColorText "    ✗ Git Bash 未找到，请安装 Git for Windows" "Error"
        Write-ColorText "    下载地址: https://git-scm.com/download/win" "Warning"
        Read-Host "按任意键退出"
        return
    }
    Write-Host ""
    
    # 步骤4: 检查Claude Code
    Write-ColorText "[4/6] 检查Claude Code安装..." "Info"
    if (Test-Command "claude") {
        try {
            $claudeVersion = & claude --version 2>$null
            Write-ColorText "    ✓ Claude Code 已安装" "Success"
            Write-ColorText "    版本: $claudeVersion" "Success"
        } catch {
            Write-ColorText "    ⚠ Claude Code 已安装但版本检查失败" "Warning"
        }
    } else {
        Write-ColorText "    ✗ Claude Code 未安装或不在PATH中" "Error"
        Write-ColorText "    请安装: npm install -g @anthropic-ai/claude-cli" "Warning"
        Read-Host "按任意键退出"
        return
    }
    Write-Host ""
    
    # 步骤5: 检查网络连接
    Write-ColorText "[5/6] 检查网络连接..." "Info"
    if (Test-NetworkConnection $env:ANTHROPIC_BASE_URL) {
        Write-ColorText "    ✓ 网络连接正常" "Success"
    } else {
        Write-ColorText "    ⚠ 网络连接可能有问题，请检查代理设置" "Warning"
    }
    Write-Host ""
    
    # 步骤6: 创建会话日志
    Write-ColorText "[6/6] 准备启动..." "Info"
    $sessionLog = "claude-session-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
    $startTime = Get-Date
    
    @"
Claude Code 会话日志
==================
启动时间: $startTime
PowerShell版本: $($PSVersionTable.PSVersion)
工作目录: $(Get-Location)
环境变量:
  - ANTHROPIC_AUTH_TOKEN: $($env:ANTHROPIC_AUTH_TOKEN.Substring(0,10))...
  - ANTHROPIC_BASE_URL: $env:ANTHROPIC_BASE_URL

"@ | Out-File -FilePath $sessionLog -Encoding UTF8
    
    Write-ColorText "    ✓ 会话日志: $sessionLog" "Success"
    Write-Host ""
    
    # 显示启动信息
    Write-ColorText "===============================================" "Header"
    Write-ColorText "   Claude Code 即将启动" "Header"
    Write-ColorText "   重要提醒:" "Warning"
    Write-ColorText "   - 输入命令后记得按回车键" "Warning"
    Write-ColorText "   - 等待AI完成回复后再继续" "Warning"
    Write-ColorText "   - 使用 /help 查看可用命令" "Warning"
    Write-ColorText "   - 使用 /exit 退出程序" "Warning"
    Write-ColorText "===============================================" "Header"
    Write-Host ""
    
    # 启动Claude Code
    try {
        Write-ColorText "正在启动 Claude Code..." "Info"
        & claude --dangerously-skip-permissions
    } catch {
        Write-ColorText "启动失败: $($_.Exception.Message)" "Error"
        "启动失败: $($_.Exception.Message)" | Out-File -FilePath $sessionLog -Append -Encoding UTF8
    } finally {
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        Write-Host ""
        Write-ColorText "Claude Code 已退出" "Info"
        Write-ColorText "会话时长: $($duration.ToString('hh\:mm\:ss'))" "Info"
        Write-ColorText "会话日志已保存到: $sessionLog" "Success"
        
        "会话结束时间: $endTime" | Out-File -FilePath $sessionLog -Append -Encoding UTF8
        "会话时长: $($duration.ToString('hh\:mm\:ss'))" | Out-File -FilePath $sessionLog -Append -Encoding UTF8
    }
}

# 检查执行策略
if ((Get-ExecutionPolicy) -eq "Restricted") {
    Write-ColorText "PowerShell 执行策略受限，请以管理员身份运行以下命令：" "Warning"
    Write-ColorText "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" "Warning"
    Read-Host "按任意键退出"
    return
}

# 运行主函数
Start-ClaudeCode

# 等待用户按键
Read-Host "按任意键退出"
