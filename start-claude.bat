@echo off
REM Windows 10 Claude Code 快速启动批处理文件
REM 作者: Augment Agent
REM 版本: 1.0

title Claude Code 启动器

echo.
echo ===============================================
echo    Windows 10 Claude Code 启动器
echo ===============================================
echo.

REM 设置环境变量
echo [1/5] 设置环境变量...
set ANTHROPIC_AUTH_TOKEN=sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL
set ANTHROPIC_BASE_URL=https://anyrouter.top

echo     ✓ ANTHROPIC_AUTH_TOKEN: %ANTHROPIC_AUTH_TOKEN:~0,10%...
echo     ✓ ANTHROPIC_BASE_URL: %ANTHROPIC_BASE_URL%
echo.

REM 检查Git Bash是否存在
echo [2/5] 检查Git Bash环境...
if exist "C:\Program Files\git\bin\bash.exe" (
    echo     ✓ Git Bash 已安装: C:\Program Files\git\bin\bash.exe
) else (
    echo     ✗ Git Bash 未找到，请安装 Git for Windows
    echo     下载地址: https://git-scm.com/download/win
    pause
    exit /b 1
)
echo.

REM 检查Claude Code是否安装
echo [3/5] 检查Claude Code安装...
where claude >nul 2>nul
if %errorlevel% equ 0 (
    echo     ✓ Claude Code 已安装
    for /f "tokens=*" %%i in ('claude --version 2^>nul') do echo     版本: %%i
) else (
    echo     ✗ Claude Code 未安装或不在PATH中
    echo     请安装Claude Code: npm install -g @anthropic-ai/claude-cli
    pause
    exit /b 1
)
echo.

REM 检查网络连接
echo [4/5] 检查网络连接...
ping -n 1 anyrouter.top >nul 2>nul
if %errorlevel% equ 0 (
    echo     ✓ 网络连接正常
) else (
    echo     ⚠ 网络连接可能有问题，请检查代理设置
)
echo.

REM 启动Claude Code
echo [5/5] 启动Claude Code...
echo.
echo ===============================================
echo   Claude Code 即将启动
echo   重要提醒：
echo   - 输入命令后记得按回车键
echo   - 等待AI完成回复后再继续
echo   - 使用 /help 查看可用命令
echo ===============================================
echo.

REM 记录启动时间
echo 启动时间: %date% %time% > claude-session.log

REM 启动Claude Code
claude --dangerously-skip-permissions

REM 如果Claude Code退出，显示信息
echo.
echo Claude Code 已退出
echo 会话日志已保存到: claude-session.log
pause
